import * as THREE from "three";
import { EARTH_RADIUS } from "../../../../constants";
import { GUI } from "lil-gui";
import PlaneMarker from "./PlaneMarker.js";
import TextLabel from "./TextLabel.js";

// 流光粒子效果的shader - 支持渐变色
const flowingParticleShader = {
  vertexShader: `
    #include <common>
    #include <logdepthbuf_pars_vertex>
    varying vec2 vUv;
    attribute float percent;
    uniform float u_time;
    uniform float number;
    uniform float speed;
    uniform float length0;
    uniform float size;
    uniform float launchProgress; // 发射进度：控制粒子从起点开始发射
    varying float opacity;
    varying float vPercent;

    void main() {
      vUv = uv;
      vPercent = percent;
      vec4 mvPosition = modelViewMatrix * vec4( position, 1.0 );
      float l = clamp(1.0-length0,0.0,1.0);
      float dis = length(mvPosition.xyz - cameraPosition);

      // 计算基础粒子大小
      float baseSize = clamp(fract(percent*number + l - u_time*number*speed)-l ,0.0,1.) * size * (1./length0)*(1./-mvPosition.z);

      // 只有当发射进度达到当前位置时，粒子才能被激活
      // launchProgress从0到1，表示粒子发射的前沿位置
      float particleSize = 0.0;
      if(launchProgress >= percent) {
        particleSize = baseSize;
      }

      gl_PointSize = particleSize;
      opacity = particleSize;
      gl_Position = projectionMatrix * mvPosition;
      #include <logdepthbuf_vertex>
    }
  `,

  fragmentShader: `
    #ifdef GL_ES
    #include <common>
    #include <logdepthbuf_pars_fragment>
    precision mediump float;
    #endif
    varying float opacity;
    varying float vPercent;
    uniform vec3 startColor;
    uniform vec3 endColor;

    void main(){
      #include <logdepthbuf_fragment>
      if(opacity <=0.1 || distance(gl_PointCoord, vec2(0.5)) > 0.5){
        discard;
      }

      // 根据百分比计算渐变色
      vec3 gradientColor = mix(startColor, endColor, vPercent);
      gl_FragColor = vec4(gradientColor, 1.0);
    }
  `,
};

/**
 * 飞线管理类
 * 职责：
 * 1. 管理飞线的创建、更新和销毁
 * 2. 管理流光粒子动画系统
 * 3. 提供飞线相关的GUI控制
 * 4. 处理点和线的几何体管理
 */
class FlightLine {
  constructor(spherePoints) {
    this.spherePoints = spherePoints;

    // 飞线数据管理
    this.flightLines = [];
    this.pointsData = [];

    // THREE.js 组件
    this.pointsSystem = null;
    this.pointsMaterial = null;
    this.pointsGeometry = null;

    // 动画系统
    this.animationId = null;
    this.isAnimating = false;

    // GUI控制
    this.gui = null;

    // 配置参数
    this.config = this._createDefaultConfig();

    // 为了兼容性，同时初始化 shaderParams
    this.shaderParams = { ...this.config };

    // 初始化系统
    this._initialize();
  }

  /**
   * 创建默认配置
   * @private
   */
  _createDefaultConfig() {
    return {
      // 粒子外观
      pointSize: 130.0,
      animationSpeed: 2.0, // 增加整体动画速度
      startColor: "#ff0000",
      endColor: "#0000ff",

      // 流光效果
      flowingLength: 0.6,
      flowingSpeed: 30.0, // 增加粒子流动速度
      blendingMode: "additive",

      // 性能相关
      curveParticleCount: 20,
      segmentDivider: 400,
    };
  }

  /**
   * 初始化系统
   * @private
   */
  _initialize() {
    this._initPointsSystem();
    // this._createGUI();
    this._startAnimation();
  }

  /**
   * 初始化THREE.Points系统
   * @private
   */
  _initPointsSystem() {
    // 创建点几何体
    this.pointsGeometry = new THREE.BufferGeometry();

    // 创建流光粒子材质
    this.pointsMaterial = new THREE.ShaderMaterial({
      vertexShader: flowingParticleShader.vertexShader,
      fragmentShader: flowingParticleShader.fragmentShader,
      uniforms: {
        u_time: { value: 2.0 },
        number: { value: 1 },
        speed: { value: 60.0 },
        length0: { value: 0.4 },
        size: { value: this.config.pointSize },
        color: { value: new THREE.Color(0xeeee00) },
        launchProgress: { value: 1.0 }, // 设置为1.0，让所有点都可见
      },
      transparent: true,
      depthWrite: false,
      blending: this._getBlendingMode(this.config.blendingMode),
    });

    // 创建Points对象
    this.pointsSystem = new THREE.Points(this.pointsGeometry, this.pointsMaterial);
    this.pointsSystem.name = "FlightLinePoints";

    // 添加到场景
    this.spherePoints.pointsGroup.add(this.pointsSystem);
  }

  /**
   * 开始动画循环
   * @private
   */
  _startAnimation() {
    if (this.isAnimating) return;

    this.isAnimating = true;

    const animate = () => {
      if (!this.isAnimating) return;

      this.animationId = requestAnimationFrame(animate);

      // 计算时间增量
      const timeIncrement = 0.0005 * this.config.animationSpeed;

      // 更新主要粒子系统的时间uniform
      if (this.pointsMaterial && this.pointsMaterial.uniforms.u_time) {
        this.pointsMaterial.uniforms.u_time.value += timeIncrement;
      }

      // 更新所有飞线粒子的时间uniform
      this._updateFlightLineAnimations(timeIncrement);
    };

    animate();
  }

  /**
   * 更新飞线动画
   * @private
   */
  _updateFlightLineAnimations(timeIncrement) {
    this.flightLines.forEach((flight) => {
      if (flight.particles && flight.particles.length > 0) {
        flight.particles.forEach((particle) => {
          if (particle.material && particle.material.uniforms && particle.material.uniforms.u_time) {
            particle.material.uniforms.u_time.value += timeIncrement;
          }
        });
      }
    });
  }

  /**
   * 停止动画
   * @private
   */
  _stopAnimation() {
    this.isAnimating = false;
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
  }

  /**
   * 获取混合模式
   * @private
   */
  _getBlendingMode(mode) {
    switch (mode) {
      case "normal":
        return THREE.NormalBlending;
      case "additive":
        return THREE.AdditiveBlending;
      case "subtractive":
        return THREE.SubtractiveBlending;
      case "multiply":
        return THREE.MultiplyBlending;
      default:
        return THREE.AdditiveBlending;
    }
  }

  /**
   * 创建GUI控制面板
   * @private
   */
  _createGUI() {
    // 如果已存在GUI，先销毁
    if (this.gui) {
      this.gui.destroy();
    }

    // 创建新的GUI
    this.gui = new GUI({ title: "飞线粒子控制" });

    // 粒子大小控制
    this.gui
      .add(this.config, "pointSize", 10.0, 300.0, 10.0)
      .name("粒子大小")
      .onChange(() => {
        this.shaderParams.pointSize = this.config.pointSize;
        this.updateAllFlightLines();
      });

    // 动画速度控制
    this.gui
      .add(this.config, "animationSpeed", 0.1, 5.0, 0.1)
      .name("动画速度")
      .onChange(() => {
        this.shaderParams.animationSpeed = this.config.animationSpeed;
      });

    // 渐变色控制
    this.gui
      .addColor(this.config, "startColor")
      .name("头部颜色")
      .onChange(() => {
        this.shaderParams.startColor = this.config.startColor;
        this.updateAllFlightLines();
      });

    this.gui
      .addColor(this.config, "endColor")
      .name("尾部颜色")
      .onChange(() => {
        this.shaderParams.endColor = this.config.endColor;
        this.updateAllFlightLines();
      });
  }

  /**
   * 重置流光粒子参数到默认值
   */
  resetShaderParams() {
    // 重置为默认配置
    const defaultConfig = this._createDefaultConfig();
    this.config = { ...defaultConfig };
    this.shaderParams = { ...defaultConfig };

    // 更新所有飞线
    this.updateAllFlightLines();

    // 刷新GUI显示
    if (this.gui) {
      this.gui.controllersRecursive().forEach((controller) => controller.updateDisplay());
    }

    console.log("流光粒子参数已重置");
  }

  /**
   * 获取当前shader参数
   * @returns {object} 当前参数配置
   */
  getShaderParams() {
    return { ...this.shaderParams };
  }

  /**
   * 使用THREE.Points创建点
   * @param {THREE.Vector3} position - 点的位置
   * @param {object} options - 点的配置选项
   * @returns {object} 点的数据对象
   */
  createPointWithShader(position, options = {}) {
    const { color = 0xff0000, size = 1.0, opacity = 0.8, name = "Point", originalCoords = null, progress = 0.0, curvePosition = 0.0 } = options;

    // 创建点数据
    const pointData = {
      id: `point_${Date.now()}_${Math.random()}`,
      position: position.clone(),
      color: new THREE.Color(color),
      size: size,
      opacity: opacity,
      name: name,
      originalCoords: originalCoords,
      progress: progress,
      curvePosition: curvePosition,
      options: options,
    };

    // 添加到点数据数组
    this.pointsData.push(pointData);

    // 更新几何体
    this.updatePointsGeometry();

    return pointData;
  }

  /**
   * 更新Points几何体
   */
  updatePointsGeometry() {
    if (this.pointsData.length === 0) {
      return;
    }

    const positions = [];
    const colors = [];
    const scales = [];
    const progresses = [];
    const curvePositions = [];
    const percents = [];

    this.pointsData.forEach((point) => {
      // 位置
      positions.push(point.position.x, point.position.y, point.position.z);

      // 颜色
      colors.push(point.color.r, point.color.g, point.color.b);

      // 缩放
      scales.push(point.size);

      // 动画进度
      progresses.push(point.progress || 0.0);

      // 在曲线上的位置（0=头部，1=尾部）
      curvePositions.push(point.curvePosition || 0.0);

      // percent属性，用于shader中的显示控制（对于起点和终点，设置为0.0确保可见）
      percents.push(point.progress || 0.0);
    });

    // 更新几何体属性
    this.pointsGeometry.setAttribute("position", new THREE.Float32BufferAttribute(positions, 3));
    this.pointsGeometry.setAttribute("aColor", new THREE.Float32BufferAttribute(colors, 3));
    this.pointsGeometry.setAttribute("aScale", new THREE.Float32BufferAttribute(scales, 1));
    this.pointsGeometry.setAttribute("aProgress", new THREE.Float32BufferAttribute(progresses, 1));
    this.pointsGeometry.setAttribute("aCurvePosition", new THREE.Float32BufferAttribute(curvePositions, 1));
    this.pointsGeometry.setAttribute("percent", new THREE.Float32BufferAttribute(percents, 1));

    // 标记需要更新
    this.pointsGeometry.attributes.position.needsUpdate = true;
    this.pointsGeometry.attributes.aColor.needsUpdate = true;
    this.pointsGeometry.attributes.aScale.needsUpdate = true;
    if (this.pointsGeometry.attributes.aProgress) this.pointsGeometry.attributes.aProgress.needsUpdate = true;
    if (this.pointsGeometry.attributes.aCurvePosition) this.pointsGeometry.attributes.aCurvePosition.needsUpdate = true;
    if (this.pointsGeometry.attributes.percent) this.pointsGeometry.attributes.percent.needsUpdate = true;
  }

  /**
   * 使用shader点系统添加自定义位置的点
   * @param {number} latitude - 纬度
   * @param {number} longitude - 经度
   * @param {object} options - 点的配置选项
   * @returns {object} 创建的点数据对象
   */
  addCustomPointWithShader(latitude, longitude, options = {}) {
    const position = this.spherePoints.latLonToVector3(latitude, longitude);
    const pointData = this.createPointWithShader(position, {
      ...options,
      originalCoords: {
        latitude: latitude,
        longitude: longitude,
      },
    });

    console.log(`Shader点已添加: (${latitude}°, ${longitude}°) -> 3D坐标(${position.x.toFixed(2)}, ${position.y.toFixed(2)}, ${position.z.toFixed(2)})`);

    return pointData;
  }

  /**
   * 使用shader点系统在两个经纬度坐标之间创建撒点和飞线效果
   * @param {number} lat1 - 起点纬度
   * @param {number} lon1 - 起点经度
   * @param {number} lat2 - 终点纬度
   * @param {number} lon2 - 终点经度
   * @param {object} options - 配置选项
   * @param {object} options.pointOptions - 点标识配置
   * @param {object} options.lineOptions - 线条配置
   * @param {object} options.animationOptions - 动画配置
   * @param {object} options.labelOptions - 文字标签配置
   * @param {string} options.labelOptions.text - 标签文字内容（默认："标签"）
   * @param {number} options.labelOptions.fontSize - 字体大小（默认：36）
   * @param {number} options.labelOptions.scale - 整体缩放（默认：1.5）
   * @param {string} options.labelOptions.color - 文字颜色（默认："#ffffff"）
   * @param {string} options.labelOptions.backgroundColor - 背景色（默认："rgba(255, 255, 255, 0.2)"）
   * @param {string} options.labelOptions.borderColor - 边框颜色（默认："#ffffff"）
   * @param {number} options.labelOptions.borderWidth - 边框宽度（默认：2）
   * @param {number} options.labelOptions.padding - 内边距（默认：12）
   * @param {number} options.labelOptions.borderRadius - 圆角半径（默认：6）
   * @param {boolean} options.labelOptions.enabled - 是否启用标签（默认：true）
   * @param {string} options.labelOptions.startBackgroundColor - 起点标签背景色（默认："rgba(255, 0, 0, 0.8)"）
   * @param {string} options.labelOptions.endBackgroundColor - 终点标签背景色（默认："rgba(0, 255, 0, 0.8)"）
   * @param {object} cardInfo - 攻击卡片信息
   * @param {Array} startArray - 起点数组信息
   * @param {Array} endArray - 终点数组信息
   */
  createFlightLineWithShader(lat1, lon1, lat2, lon2, options = {}, cardInfo = null, startArray = null, endArray = null) {
    const { pointOptions = {}, lineOptions = {}, animationOptions = {}, labelOptions = {} } = options;

    // 默认点配置（适配shader系统）
    const defaultPointOptions = {
      color: 0x00ff00,
      size: 1.5, // shader点的大小
      opacity: 0.9,
    };

    // 默认线条配置
    const defaultLineOptions = {
      color: 0x00ffff,
      opacity: 0.6,
      lineWidth: 2,
      segments: 50,
      height: 5, // 飞线的高度偏移
    };

    // 默认动画配置
    const defaultAnimationOptions = {
      duration: 3000, // 动画持续时间(ms)
      particleCount: 5, // 粒子数量
      particleSize: 0.1,
      particleColor: 0xffffff,
    };

    // 默认标签配置
    const defaultLabelOptions = {
      text: "标签", // 标签文字
      fontSize: 32, // 统一字体大小
      scale: 2.5, // 整体缩放
      color: "#ffffff", // 文字颜色
      backgroundColor: "transparent", // 透明背景色
      borderColor: "#ffffff", // 边框颜色
      borderWidth: 2, // 边框宽度
      padding: 8, // 减小内边距
      borderRadius: 6, // 圆角半径
      enabled: true, // 是否启用标签
      startBackgroundColor: "transparent", // 起点标签透明背景色
      endBackgroundColor: "transparent", // 终点标签透明背景色
    };

    const finalPointOptions = { ...defaultPointOptions, ...pointOptions };
    const finalLineOptions = { ...defaultLineOptions, ...lineOptions };
    const finalAnimationOptions = { ...defaultAnimationOptions, ...animationOptions };
    const finalLabelOptions = { ...defaultLabelOptions, ...labelOptions };

    // 获取起点和终点的3D坐标
    const startPos = this.spherePoints.latLonToVector3(lat1, lon1);
    const endPos = this.spherePoints.latLonToVector3(lat2, lon2);

    // 将位置稍微提高，避免被地球表面遮挡
    const heightOffset = 0.05; // 高度偏移量
    const startPosElevated = startPos
      .clone()
      .normalize()
      .multiplyScalar(startPos.length() + heightOffset);
    const endPosElevated = endPos
      .clone()
      .normalize()
      .multiplyScalar(endPos.length() + heightOffset);

    // 使用PlaneMarker创建起点标识（红色扩散光环）
    const startPoint = PlaneMarker.createStartMarker(startPosElevated, {
      color: 0xff0000, // 红色
      ringColor: 0xff4444, // 稍亮的红色光环
      size: 0.7, // 减小基础大小
      ringRadius: 0.5, // 减小扩散范围
      ringWidth: 0.15, // 减小扩散环宽度
      intensity: 1.8, // 降低强度
      opacity: finalPointOptions.opacity || 0.8,
      useGlowEffect: true, // 使用扩散光环效果
      name: `FlightStart_${Date.now()}`,
    });

    // 使用PlaneMarker创建终点标识（绿色扩散光环）
    const endPoint = PlaneMarker.createEndMarker(endPosElevated, {
      color: 0x00ff00, // 绿色
      ringColor: 0x44ff44, // 稍亮的绿色光环
      size: 0.5, // 减小基础大小
      ringRadius: 0.5, // 减小扩散范围
      ringWidth: 0.15, // 减小扩散环宽度
      intensity: 1.8, // 降低强度
      opacity: finalPointOptions.opacity || 0.8,
      useGlowEffect: true, // 使用扩散光环效果
      name: `FlightEnd_${Date.now()}`,
    });

    // 创建文字标签（如果启用）
    let startLabel = null;
    let endLabel = null;

    if (finalLabelOptions.enabled) {
      // 确定起点标签文字内容
      let startLabelText = finalLabelOptions.text;
      if (startArray && startArray.name) {
        startLabelText = startArray.name;
      }

      // 确定终点标签文字内容
      let endLabelText = finalLabelOptions.text;
      if (endArray && Array.isArray(endArray) && endArray.length > 0 && endArray[0].name) {
        endLabelText = endArray[0].name;
      }

      console.log(`创建标签 - 起点: "${startLabelText}", 终点: "${endLabelText}"`);

      // 创建起点文字标签
      const startLabelPos = startPosElevated
        .clone()
        .normalize()
        .multiplyScalar(startPosElevated.length() + 0.3);
      startLabel = TextLabel.createTextLabel(startLabelPos, startLabelText, {
        fontSize: finalLabelOptions.fontSize,
        color: finalLabelOptions.color,
        backgroundColor: finalLabelOptions.startBackgroundColor,
        borderColor: finalLabelOptions.borderColor,
        borderWidth: finalLabelOptions.borderWidth,
        padding: finalLabelOptions.padding,
        borderRadius: finalLabelOptions.borderRadius,
        scale: finalLabelOptions.scale,
        name: `StartLabel_${Date.now()}`,
      });

      // 创建终点文字标签
      const endLabelPos = endPosElevated
        .clone()
        .normalize()
        .multiplyScalar(endPosElevated.length() + 0.3);
      endLabel = TextLabel.createTextLabel(endLabelPos, endLabelText, {
        fontSize: finalLabelOptions.fontSize,
        color: finalLabelOptions.color,
        backgroundColor: finalLabelOptions.endBackgroundColor,
        borderColor: finalLabelOptions.borderColor,
        borderWidth: finalLabelOptions.borderWidth,
        padding: finalLabelOptions.padding,
        borderRadius: finalLabelOptions.borderRadius,
        scale: finalLabelOptions.scale,
        name: `EndLabel_${Date.now()}`,
      });
    }

    // 创建飞线（使用原始位置，因为飞线本身不需要提高）
    const flightLine = this.createCurvedLine(lat1, lon1, lat2, lon2, finalLineOptions);

    // 将起点和终点标识添加到场景中
    this.spherePoints.pointsGroup.add(startPoint);
    this.spherePoints.pointsGroup.add(endPoint);

    // 将文字标签添加到场景中（如果存在）
    if (startLabel) {
      this.spherePoints.pointsGroup.add(startLabel);
      startLabel.visible = false;
    }
    if (endLabel) {
      this.spherePoints.pointsGroup.add(endLabel);
      endLabel.visible = false;
    }

    // 初始时隐藏标识，等待动画开始时显示
    startPoint.visible = false;
    endPoint.visible = false;

    const labelStatus = finalLabelOptions.enabled ? "及文字标签" : "";
    console.log(`起点和终点PlaneMarker标识${labelStatus}已添加到场景，初始隐藏，等待动画开始`);

    // 创建动画粒子（使用shader系统）
    const particles = this.createFlightParticlesWithShader(flightLine, finalAnimationOptions);

    // 保存飞线信息
    const flightLineData = {
      id: `flight_${Date.now()}`,
      startPoint,
      endPoint,
      startLabel,
      endLabel,
      line: flightLine,
      particles,
      startCoords: { lat: lat1, lon: lon1 },
      endCoords: { lat: lat2, lon: lon2 },
      options: { finalPointOptions, finalLineOptions, finalAnimationOptions },
      useShader: true, // 标记使用shader系统
      animationState: {
        lineDrawn: false,
        flowStarted: false,
      },
      // 添加攻击数据信息
      cardInfo: cardInfo,
      startArray: startArray,
      endArray: endArray,
    };

    this.flightLines.push(flightLineData);

    console.log(`Shader飞线已创建: (${lat1}°, ${lon1}°) -> (${lat2}°, ${lon2}°)`);

    return flightLineData;
  }

  /**
   * 在两个经纬度坐标之间创建撒点和飞线效果（保留原有方法用于兼容性）
   * @param {number} lat1 - 起点纬度
   * @param {number} lon1 - 起点经度
   * @param {number} lat2 - 终点纬度
   * @param {number} lon2 - 终点经度
   * @param {object} options - 配置选项
   */
  createFlightLine(lat1, lon1, lat2, lon2, options = {}) {
    const { pointOptions = {}, lineOptions = {}, animationOptions = {} } = options;

    // 默认点配置
    const defaultPointOptions = {
      color: 0x00ff00,
      size: 0.15,
      opacity: 0.9,
      emissive: 0x004400,
    };

    // 默认线条配置
    const defaultLineOptions = {
      color: 0x00ffff,
      opacity: 0.6,
      lineWidth: 2,
      segments: 50,
      height: 5, // 飞线的高度偏移
    };

    // 默认动画配置
    const defaultAnimationOptions = {
      duration: 3000, // 动画持续时间(ms)
      particleCount: 5, // 粒子数量
      particleSize: 0.1,
      particleColor: 0xffffff,
    };

    const finalPointOptions = { ...defaultPointOptions, ...pointOptions };
    const finalLineOptions = { ...defaultLineOptions, ...lineOptions };
    const finalAnimationOptions = { ...defaultAnimationOptions, ...animationOptions };

    // 使用shader系统创建起点和终点
    const startPoint = this.addCustomPointWithShader(lat1, lon1, {
      color: finalPointOptions.color,
      size: finalPointOptions.size * 10, // 适配shader系统的大小
      opacity: finalPointOptions.opacity,
      name: `FlightStart_${Date.now()}`,
    });

    const endPoint = this.addCustomPointWithShader(lat2, lon2, {
      color: finalPointOptions.color,
      size: finalPointOptions.size * 10, // 适配shader系统的大小
      opacity: finalPointOptions.opacity,
      name: `FlightEnd_${Date.now()}`,
    });

    // 创建飞线
    const flightLine = this.createCurvedLine(lat1, lon1, lat2, lon2, finalLineOptions);

    // 使用流光粒子系统创建动画粒子
    const particles = this.createFlightParticlesWithShader(flightLine, {
      ...finalAnimationOptions,
      particleColor: finalLineOptions.color, // 使用线条颜色作为粒子颜色
    });

    // 保存飞线信息
    const flightLineData = {
      id: `flight_${Date.now()}`,
      startPoint,
      endPoint,
      line: flightLine,
      particles,
      startCoords: { lat: lat1, lon: lon1 },
      endCoords: { lat: lat2, lon: lon2 },
      options: { finalPointOptions, finalLineOptions, finalAnimationOptions },
      animationState: {
        lineDrawn: false,
        flowStarted: false,
      },
    };

    this.flightLines.push(flightLineData);

    console.log(`飞线已创建: (${lat1}°, ${lon1}°) -> (${lat2}°, ${lon2}°)`);

    return flightLineData;
  }

  /**
   * 创建弧形飞线
   * @param {number} lat1 - 起点纬度
   * @param {number} lon1 - 起点经度
   * @param {number} lat2 - 终点纬度
   * @param {number} lon2 - 终点经度
   * @param {object} options - 线条配置
   */
  createCurvedLine(lat1, lon1, lat2, lon2, options) {
    const { color, opacity, segments, height } = options;

    // 获取起点和终点的3D坐标
    const startPos = this.spherePoints.latLonToVector3(lat1, lon1);
    const endPos = this.spherePoints.latLonToVector3(lat2, lon2);

    const dis = startPos.distanceTo(endPos);

    // 设置最小高度限制，确保即使距离很近的点也有足够的弧度
    const minHeight = 2.0; // 最小弧高
    const calculatedHeight = dis * 0.4;
    const arcHeight = Math.max(minHeight, calculatedHeight);

    // 使用 CatmullRomCurve3 创建平滑曲线
    // 1. 起点和终点在球面上
    const surfaceOffset = 0.05; // 很小的偏移，让点在球面上但略微向外
    const safeStartPos = startPos
      .clone()
      .normalize()
      .multiplyScalar(EARTH_RADIUS + surfaceOffset);
    const safeEndPos = endPos
      .clone()
      .normalize()
      .multiplyScalar(EARTH_RADIUS + surfaceOffset);

    // 2. 计算中间控制点 - 使用球面几何
    const midPoint = new THREE.Vector3().addVectors(startPos, endPos).multiplyScalar(0.5);
    midPoint.normalize().multiplyScalar(EARTH_RADIUS + arcHeight);

    // 3. 使用二次贝塞尔曲线，它在端点处没有弯折问题
    const curve = new THREE.QuadraticBezierCurve3(safeStartPos, midPoint, safeEndPos);

    // 生成曲线上的点，但只使用有效部分（排除延伸的控制点影响）
    // CatmullRom曲线会在t=0到t=1之间生成从第二个控制点到第四个控制点的平滑曲线
    const allPoints = curve.getPoints(segments);

    // 由于我们使用了5个控制点，CatmullRom会生成从safeStartPos到safeEndPos的平滑曲线
    // 不需要额外过滤，因为曲线本身就是从起点到终点
    const points = allPoints;

    // 简化的后处理：确保所有点都在球面外部
    const processedPoints = points.map((point, index) => {
      const distanceFromCenter = point.length();
      const isEndPoint = index === 0 || index === points.length - 1;

      if (isEndPoint) {
        // 起点和终点：保持在球面上，只需很小的偏移
        const minDistance = EARTH_RADIUS + surfaceOffset;

        if (distanceFromCenter < minDistance) {
          return point.normalize().multiplyScalar(minDistance);
        }
        return point;
      } else {
        // 中间点：确保不穿入球体
        const minDistance = EARTH_RADIUS + 0.1; // 使用较小的安全距离，因为CatmullRom更平滑

        if (distanceFromCenter < minDistance) {
          return point.normalize().multiplyScalar(minDistance);
        }
        return point;
      }
    });

    // CatmullRomCurve3 本身就很平滑，不需要额外的平滑处理

    // 创建几何体
    const geometry = new THREE.BufferGeometry().setFromPoints(processedPoints);

    // 添加顶点索引属性用于绘制动画
    const indices = new Float32Array(processedPoints.length);
    for (let i = 0; i < processedPoints.length; i++) {
      indices[i] = i;
    }
    geometry.setAttribute("position_index", new THREE.BufferAttribute(indices, 1));

    // 创建自定义着色器材质支持线条绘制动画
    const material = new THREE.ShaderMaterial({
      uniforms: {
        color: { value: new THREE.Color(color) },
        opacity: { value: opacity },
        drawProgress: { value: 0.0 }, // 绘制进度 0-1
      },
      vertexShader: `
        attribute float position_index;
        uniform float drawProgress;
        varying float vVisible;

        void main() {
          // 计算当前顶点是否应该可见
          float totalVertices = ${processedPoints.length}.0;
          vVisible = step(position_index, drawProgress * totalVertices);

          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform vec3 color;
        uniform float opacity;
        varying float vVisible;

        void main() {
          if (vVisible < 0.5) discard;
          gl_FragColor = vec4(color, opacity);
        }
      `,
      transparent: true,
    });

    // 创建线条对象
    const line = new THREE.Line(geometry, material);
    line.name = `FlightLine_${Date.now()}`;
    line.visible = false; // 初始不可见，等待动画开始时设置为可见

    const curveLength = curve.getLength();

    // 存储曲线信息到userData
    line.userData = {
      curve: curve,
      curveLength: curveLength,
      startPos: safeStartPos,
      endPos: safeEndPos,
      processedPoints: processedPoints, // 保存处理后的点用于调试
      material: material, // 保存材质引用用于动画控制
      lineColor: color, // 保存线条颜色用于粒子材质
      isDrawing: false, // 是否正在绘制
      drawAnimationId: null, // 绘制动画ID
    };

    // 添加到场景
    this.spherePoints.pointsGroup.add(line);

    return line;
  }

  /**
   * 为飞线添加绘制动画
   * @param {THREE.Line} flightLine - 飞线对象
   * @param {number} duration - 绘制动画持续时间（毫秒）
   * @param {Function} onComplete - 绘制完成回调
   */
  animateLineDrawing(flightLine, duration = 2000, onComplete = null) {
    const material = flightLine.userData?.material;
    if (!material || !material.uniforms) {
      console.warn("飞线缺少可动画的材质");
      return;
    }

    // 停止之前的绘制动画
    if (flightLine.userData.drawAnimationId) {
      cancelAnimationFrame(flightLine.userData.drawAnimationId);
    }

    // 设置飞线为可见，开始动画
    flightLine.visible = true;

    flightLine.userData.isDrawing = true;
    const startTime = performance.now();

    const animate = (currentTime) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 使用easeOutCubic缓动函数
      const easedProgress = 1 - Math.pow(1 - progress, 3);

      // 更新绘制进度
      material.uniforms.drawProgress.value = easedProgress;

      if (progress >= 1) {
        // 绘制动画完成
        flightLine.userData.isDrawing = false;
        flightLine.userData.drawAnimationId = null;
        console.log("飞线绘制动画完成");
        if (onComplete) onComplete();
        return;
      }

      flightLine.userData.drawAnimationId = requestAnimationFrame(animate);
    };

    console.log(`开始飞线绘制动画 - 持续时间: ${duration}ms`);
    flightLine.userData.drawAnimationId = requestAnimationFrame(animate);
  }

  /**
   * 停止飞线绘制动画
   * @param {THREE.Line} flightLine - 飞线对象
   */
  stopLineDrawing(flightLine) {
    if (flightLine.userData?.drawAnimationId) {
      cancelAnimationFrame(flightLine.userData.drawAnimationId);
      flightLine.userData.drawAnimationId = null;
      flightLine.userData.isDrawing = false;
    }
  }

  /**
   * 重置飞线绘制状态
   * @param {THREE.Line} flightLine - 飞线对象
   */
  resetLineDrawing(flightLine) {
    this.stopLineDrawing(flightLine);

    // 设置飞线为不可见
    flightLine.visible = false;

    const material = flightLine.userData?.material;
    if (material && material.uniforms) {
      material.uniforms.drawProgress.value = 0.0;
    }
  }

  /**
   * 使用流光粒子系统创建飞行粒子动画
   * @param {THREE.Line} flightLine - 飞线对象
   * @param {object} options - 动画配置
   */
  createFlightParticlesWithShader(flightLine, options) {
    // 从飞线几何体获取曲线信息
    const curve = flightLine.userData?.curve;
    if (!curve) {
      console.warn("飞线缺少曲线信息");
      return [];
    }

    // 计算分段数
    const segment = Math.ceil(curve.getLength()) * 50;
    const stepPts = curve.getSpacedPoints(segment);

    // 创建流光粒子几何体
    const lineGeo = new THREE.BufferGeometry().setFromPoints(stepPts);
    const length = stepPts.length;
    var percents = new Float32Array(length);
    for (let i = 0; i < length; i += 1) {
      percents[i] = i / length;
    }
    lineGeo.setAttribute("percent", new THREE.BufferAttribute(percents, 1));

    // 创建流光粒子材质（使用飞线的颜色）
    const lineColor = flightLine.material?.color || flightLine.userData?.lineColor;
    let flowMat = this.getPointFlyLineMaterial(segment, lineColor);

    // 创建粒子点系统
    const point = new THREE.Points(lineGeo, flowMat);
    point.scale.copy(flightLine.scale);
    point.visible = false; // 初始不可见，等待动画开始时设置为可见

    // 设置动画更新函数
    point.onBeforeRender = () => {
      if (point.userData.isFlowing) {
        // 只有在流光状态时才更新时间动画
        flowMat.uniforms.u_time.value += 0.0005 * this.shaderParams.animationSpeed;
      }
    };

    // 添加到场景
    this.spherePoints.pointsGroup.add(point);

    // 保存粒子状态信息
    point.userData = {
      isFlowing: false, // 是否正在流光动画
      isLaunching: false, // 是否正在发射动画
      launchAnimationId: null, // 发射动画ID
    };

    return [{ point, material: flowMat, geometry: lineGeo }];
  }

  /**
   * 启动飞线流光动画（从起始点发射）
   * @param {Array} particles - 粒子数组
   * @param {number} delay - 延迟时间（毫秒）
   * @param {number} launchDuration - 发射动画时长（毫秒，默认2000）
   */
  startFlowAnimation(particles, delay = 0, launchDuration = 2000) {
    setTimeout(() => {
      particles.forEach((particle) => {
        if (particle.point) {
          this.animateParticleLaunch(particle, launchDuration);
        }
      });
    }, delay);
  }

  /**
   * 粒子发射动画（从起始点开始发射）
   * @param {Object} particle - 粒子对象
   * @param {number} duration - 发射动画时长（毫秒）
   */
  animateParticleLaunch(particle, duration = 2000) {
    const point = particle.point;
    const material = particle.material;

    // 停止之前的发射动画
    if (point.userData.launchAnimationId) {
      cancelAnimationFrame(point.userData.launchAnimationId);
    }

    // 设置粒子为可见，开始动画
    point.visible = true;

    point.userData.isLaunching = true;
    point.userData.isFlowing = true; // 同时启动流光动画

    const startTime = performance.now();

    const animate = (currentTime) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 使用easeOutQuart缓动函数（先快后慢，适合发射效果）
      const easedProgress = 1 - Math.pow(1 - progress, 4);

      // 更新发射进度，让粒子从起始位置逐渐发射到终点
      if (material.uniforms.launchProgress) {
        material.uniforms.launchProgress.value = easedProgress;
      }

      if (progress >= 1) {
        // 发射动画完成
        point.userData.isLaunching = false;
        point.userData.launchAnimationId = null;
        console.log("粒子发射动画完成，所有粒子已发射");
        return;
      }

      point.userData.launchAnimationId = requestAnimationFrame(animate);
    };

    console.log("开始粒子发射动画（从起始点发射）");
    point.userData.launchAnimationId = requestAnimationFrame(animate);
  }

  /**
   * 停止飞线流光动画
   * @param {Array} particles - 粒子数组
   */
  stopFlowAnimation(particles) {
    particles.forEach((particle) => {
      if (particle.point) {
        // 停止发射动画
        if (particle.point.userData.launchAnimationId) {
          cancelAnimationFrame(particle.point.userData.launchAnimationId);
          particle.point.userData.launchAnimationId = null;
        }

        // 停止流光动画
        particle.point.userData.isFlowing = false;
        particle.point.userData.isLaunching = false;

        console.log("粒子动画已停止");
      }
    });
  }

  /**
   * 重置粒子发射进度
   * @param {Array} particles - 粒子数组
   */
  resetParticleLaunch(particles) {
    particles.forEach((particle) => {
      if (particle.point && particle.material) {
        // 停止所有动画
        if (particle.point.userData.launchAnimationId) {
          cancelAnimationFrame(particle.point.userData.launchAnimationId);
          particle.point.userData.launchAnimationId = null;
        }

        // 重置状态
        particle.point.userData.isLaunching = false;
        particle.point.userData.isFlowing = false;

        // 设置粒子为不可见
        particle.point.visible = false;

        // 重置发射进度
        if (particle.material.uniforms.launchProgress) {
          particle.material.uniforms.launchProgress.value = 0.0;
        }
      }
    });
  }

  /**
   * 启动完整的飞线动画序列（绘制线条 -> 流光动画）
   * @param {Object} flightLineData - 飞线数据对象
   * @param {Object} options - 动画选项
   */
  startFlightLineAnimation(flightLineData, options = {}) {
    const {
      drawDuration = 1500, // 线条绘制时间
      flowDelay = 200, // 流光动画延迟时间
    } = options;

    // 重置动画状态
    flightLineData.animationState.lineDrawn = false;
    flightLineData.animationState.flowStarted = false;

    // 重置线条绘制状态
    this.resetLineDrawing(flightLineData.line);

    // 在动画开始时显示起点和终点标识（带透明度渐变）
    if (flightLineData.useShader && flightLineData.startPoint && flightLineData.endPoint) {
      // 启动扩散光环的淡入动画
      this.fadeInMarker(flightLineData.startPoint, 800); // 800ms淡入
      this.fadeInMarker(flightLineData.endPoint, 800); // 800ms淡入
      console.log(`飞线动画开始，淡入显示飞线 ${flightLineData.id} 的起点和终点扩散光环`);
    }

    // 在动画开始时显示文字标签（带透明度渐变）
    if (flightLineData.startLabel) {
      TextLabel.fadeInLabel(flightLineData.startLabel, 1000); // 1000ms淡入
    }
    if (flightLineData.endLabel) {
      TextLabel.fadeInLabel(flightLineData.endLabel, 1000); // 1000ms淡入
    }
    if (flightLineData.startLabel || flightLineData.endLabel) {
      console.log(`飞线动画开始，淡入显示飞线 ${flightLineData.id} 的文字标签`);
    }

    console.log(`开始飞线动画序列: ${flightLineData.id}`);

    // 第一阶段：绘制线条
    this.animateLineDrawing(flightLineData.line, drawDuration, () => {
      flightLineData.animationState.lineDrawn = true;
      console.log("线条绘制完成，准备启动流光动画");

      // 第二阶段：启动流光动画
      this.startFlowAnimation(flightLineData.particles, flowDelay);
      flightLineData.animationState.flowStarted = true;
    });
  }

  /**
   * 停止飞线动画序列
   * @param {Object} flightLineData - 飞线数据对象
   */
  stopFlightLineAnimation(flightLineData) {
    // 停止线条绘制动画
    this.stopLineDrawing(flightLineData.line);

    // 停止流光动画
    this.stopFlowAnimation(flightLineData.particles);

    // 重置状态
    flightLineData.animationState.lineDrawn = false;
    flightLineData.animationState.flowStarted = false;

    console.log(`飞线动画已停止: ${flightLineData.id}`);
  }

  /**
   * 重置飞线动画到初始状态
   * @param {Object} flightLineData - 飞线数据对象
   */
  resetFlightLineAnimation(flightLineData) {
    // 停止线条绘制动画
    this.stopLineDrawing(flightLineData.line);

    // 重置粒子发射进度
    this.resetParticleLaunch(flightLineData.particles);

    // 重置线条绘制状态
    this.resetLineDrawing(flightLineData.line);

    // 重置动画状态
    flightLineData.animationState.lineDrawn = false;
    flightLineData.animationState.flowStarted = false;

    // 淡出隐藏起点和终点标识
    if (flightLineData.useShader && flightLineData.startPoint && flightLineData.endPoint) {
      this.fadeOutMarker(flightLineData.startPoint, 600); // 600ms淡出
      this.fadeOutMarker(flightLineData.endPoint, 600); // 600ms淡出
      console.log(`飞线动画重置，淡出隐藏飞线 ${flightLineData.id} 的起点和终点扩散光环`);
    }

    // 淡出隐藏文字标签
    if (flightLineData.startLabel) {
      TextLabel.fadeOutLabel(flightLineData.startLabel, 600); // 600ms淡出
    }
    if (flightLineData.endLabel) {
      TextLabel.fadeOutLabel(flightLineData.endLabel, 600); // 600ms淡出
    }
    if (flightLineData.startLabel || flightLineData.endLabel) {
      console.log(`飞线动画重置，淡出隐藏飞线 ${flightLineData.id} 的文字标签`);
    }

    console.log(`飞线动画已重置: ${flightLineData.id}`);
  }

  /**
   * 创建流光粒子材质 - 支持渐变色
   * @param {number} segment - 分段数
   * @param {number|string} color - 粒子颜色（可选，默认使用配置中的渐变色）
   */
  getPointFlyLineMaterial(segment, color = null) {
    // 如果提供了颜色参数，使用该颜色作为起始和结束颜色
    let startColor, endColor;
    if (color !== null) {
      const baseColor = new THREE.Color(color);
      startColor = baseColor;
      endColor = baseColor.clone().multiplyScalar(0.3); // 尾部颜色稍暗
    } else {
      // 使用配置中的渐变色
      startColor = new THREE.Color(this.shaderParams.startColor);
      endColor = new THREE.Color(this.shaderParams.endColor);
    }

    return new THREE.ShaderMaterial({
      uniforms: {
        u_time: { value: 2.0 },
        number: { value: Math.round(segment / this.shaderParams.segmentDivider) || 1 },
        speed: { value: this.shaderParams.flowingSpeed / Math.round(segment / 50) || 1 },
        length0: { value: this.shaderParams.flowingLength },
        size: { value: this.shaderParams.pointSize },
        launchProgress: { value: 0.0 }, // 发射进度，初始为0
        startColor: { value: startColor },
        endColor: { value: endColor },
      },
      vertexShader: flowingParticleShader.vertexShader,
      fragmentShader: flowingParticleShader.fragmentShader,
      transparent: true,
      depthWrite: false,
      blending: this._getBlendingMode(this.shaderParams.blendingMode),
    });
  }

  /**
   * 更新所有飞线的参数
   */
  updateAllFlightLines() {
    this.flightLines.forEach((flight) => {
      if (flight.particles && flight.particles.length > 0) {
        flight.particles.forEach((particle) => {
          if (particle.material && particle.material.uniforms) {
            // 更新材质参数
            particle.material.uniforms.size.value = this.shaderParams.pointSize;
            particle.material.uniforms.length0.value = this.shaderParams.flowingLength;
            particle.material.uniforms.speed.value = this.shaderParams.flowingSpeed;
            // 更新渐变色
            particle.material.uniforms.startColor.value = new THREE.Color(this.shaderParams.startColor);
            particle.material.uniforms.endColor.value = new THREE.Color(this.shaderParams.endColor);
            particle.material.blending = this._getBlendingMode(this.shaderParams.blendingMode);
            particle.material.needsUpdate = true;
          }
        });
      }
    });
  }

  /**
   * 拖尾粒子动画
   * @param {object} particleData - 粒子数据对象
   * @param {Float32Array} pathPositions - 路径位置数组
   * @param {number} duration - 动画持续时间
   * @param {number} delay - 延迟时间
   * @param {number} trailIndex - 在拖尾中的索引
   */
  animateTrailParticle(particleData, pathPositions, duration, delay, trailIndex) {
    const startTime = Date.now() + delay;
    const pointCount = pathPositions.length / 3;
    const trailDelay = trailIndex * 50; // 拖尾延迟

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.max(0, Math.min(1, elapsed / duration));

      if (progress >= 1) {
        // 动画结束，重新开始
        setTimeout(() => {
          this.animateTrailParticle(particleData, pathPositions, duration, 0, trailIndex);
        }, 1000);
        return;
      }

      // 计算拖尾粒子的位置（落后于头部粒子）
      const trailProgress = Math.max(0, progress - trailDelay / duration);

      // 计算当前位置
      const index = Math.floor(trailProgress * (pointCount - 1)) * 3;
      const nextIndex = Math.min(index + 3, pathPositions.length - 3);

      if (index < pathPositions.length - 3 && trailProgress > 0) {
        const t = (trailProgress * (pointCount - 1)) % 1;

        // 更新粒子位置
        particleData.position.x = THREE.MathUtils.lerp(pathPositions[index], pathPositions[nextIndex], t);
        particleData.position.y = THREE.MathUtils.lerp(pathPositions[index + 1], pathPositions[nextIndex + 1], t);
        particleData.position.z = THREE.MathUtils.lerp(pathPositions[index + 2], pathPositions[nextIndex + 2], t);

        // 更新生命周期（用于shader中的效果计算）
        particleData.lifetime = trailProgress;
      }

      requestAnimationFrame(animate);
    };

    // 延迟启动动画
    if (delay > 0) {
      setTimeout(() => {
        animate();
        // 启动后立即更新几何体
        this.updatePointsGeometry();
      }, delay);
    } else {
      animate();
      this.updatePointsGeometry();
    }
  }

  /**
   * shader粒子动画（保留用于兼容性）
   * @param {object} particleData - 粒子数据对象
   * @param {Float32Array} pathPositions - 路径位置数组
   * @param {number} duration - 动画持续时间
   * @param {number} delay - 延迟时间
   */
  animateShaderParticle(particleData, pathPositions, duration, delay) {
    // 使用新的拖尾动画方法
    this.animateTrailParticle(particleData, pathPositions, duration, delay, 0);
  }

  /**
   * 更新拖尾长度
   * @param {number} newLength - 新的拖尾长度
   */
  updateTrailLength(newLength) {
    this.shaderParams.trailLength = newLength;

    // 重新创建所有飞线的粒子系统
    this.flightLines.forEach((flight) => {
      if (flight.particles && flight.particles.length > 0) {
        // 移除旧的粒子
        flight.particles.forEach((particle) => {
          const index = this.pointsData.findIndex((p) => p.id === particle.id);
          if (index > -1) {
            this.pointsData.splice(index, 1);
          }
        });

        // 重新创建粒子
        flight.particles = this.createFlightParticlesWithShader(flight.line, flight.options.finalAnimationOptions);
      }
    });

    // 更新几何体
    this.updatePointsGeometry();

    console.log(`拖尾长度已更新为: ${newLength}`);
  }

  /**
   * 移除指定的飞线
   * @param {string} flightId - 飞线ID
   */
  removeFlightLine(flightId) {
    const index = this.flightLines.findIndex((flight) => flight.id === flightId);
    if (index > -1) {
      const flight = this.flightLines[index];

      // 移除起点和终点标识
      if (flight.useShader) {
        // 对于使用shader的飞线，起点和终点是PlaneMarker对象
        if (flight.startPoint) {
          // 停止任何正在进行的淡入/淡出动画
          if (flight.startPoint.userData.fadeAnimationId) {
            cancelAnimationFrame(flight.startPoint.userData.fadeAnimationId);
          }
          this.spherePoints.pointsGroup.remove(flight.startPoint);
          // 使用PlaneMarker的销毁方法
          if (typeof PlaneMarker !== "undefined" && PlaneMarker.destroyMarker) {
            PlaneMarker.destroyMarker(flight.startPoint);
          }
        }
        if (flight.endPoint) {
          // 停止任何正在进行的淡入/淡出动画
          if (flight.endPoint.userData.fadeAnimationId) {
            cancelAnimationFrame(flight.endPoint.userData.fadeAnimationId);
          }
          this.spherePoints.pointsGroup.remove(flight.endPoint);
          // 使用PlaneMarker的销毁方法
          if (typeof PlaneMarker !== "undefined" && PlaneMarker.destroyMarker) {
            PlaneMarker.destroyMarker(flight.endPoint);
          }
        }

        // 移除文字标签
        if (flight.startLabel) {
          this.spherePoints.pointsGroup.remove(flight.startLabel);
          TextLabel.destroyLabel(flight.startLabel);
        }
        if (flight.endLabel) {
          this.spherePoints.pointsGroup.remove(flight.endLabel);
          TextLabel.destroyLabel(flight.endLabel);
        }
      } else {
        // 对于传统飞线，使用原有的removePoint方法
        if (this.removePoint) {
          this.removePoint(flight.startPoint);
          this.removePoint(flight.endPoint);
        }
      }

      // 移除飞线
      this.spherePoints.pointsGroup.remove(flight.line);
      if (flight.line.geometry) flight.line.geometry.dispose();
      if (flight.line.material) flight.line.material.dispose();

      // 移除流光粒子
      flight.particles.forEach((particle) => {
        if (particle.point) {
          this.spherePoints.pointsGroup.remove(particle.point);
          if (particle.geometry) particle.geometry.dispose();
          if (particle.material) particle.material.dispose();
        }
      });

      // 从数组中移除
      this.flightLines.splice(index, 1);

      console.log(`飞线已移除: ${flightId}`);
    }
  }

  /**
   * 清除所有飞线
   */
  clearAllFlightLines() {
    this.flightLines.forEach((flight) => {
      // 移除起点和终点标识
      if (flight.useShader) {
        // 对于使用shader的飞线，起点和终点是PlaneMarker对象
        if (flight.startPoint) {
          // 停止任何正在进行的淡入/淡出动画
          if (flight.startPoint.userData.fadeAnimationId) {
            cancelAnimationFrame(flight.startPoint.userData.fadeAnimationId);
          }
          this.spherePoints.pointsGroup.remove(flight.startPoint);
          // 使用PlaneMarker的销毁方法
          if (typeof PlaneMarker !== "undefined" && PlaneMarker.destroyMarker) {
            PlaneMarker.destroyMarker(flight.startPoint);
          }
        }
        if (flight.endPoint) {
          // 停止任何正在进行的淡入/淡出动画
          if (flight.endPoint.userData.fadeAnimationId) {
            cancelAnimationFrame(flight.endPoint.userData.fadeAnimationId);
          }
          this.spherePoints.pointsGroup.remove(flight.endPoint);
          // 使用PlaneMarker的销毁方法
          if (typeof PlaneMarker !== "undefined" && PlaneMarker.destroyMarker) {
            PlaneMarker.destroyMarker(flight.endPoint);
          }
        }

        // 移除文字标签
        if (flight.startLabel) {
          this.spherePoints.pointsGroup.remove(flight.startLabel);
          TextLabel.destroyLabel(flight.startLabel);
        }
        if (flight.endLabel) {
          this.spherePoints.pointsGroup.remove(flight.endLabel);
          TextLabel.destroyLabel(flight.endLabel);
        }
      } else {
        // 对于传统飞线，使用原有的removePoint方法
        if (this.removePoint) {
          this.removePoint(flight.startPoint);
          this.removePoint(flight.endPoint);
        }
      }

      // 移除飞线
      this.spherePoints.pointsGroup.remove(flight.line);
      if (flight.line.geometry) flight.line.geometry.dispose();
      if (flight.line.material) flight.line.material.dispose();

      // 移除流光粒子
      flight.particles.forEach((particle) => {
        if (particle.point) {
          this.spherePoints.pointsGroup.remove(particle.point);
          if (particle.geometry) particle.geometry.dispose();
          if (particle.material) particle.material.dispose();
        }
      });
    });

    this.flightLines = [];
    console.log("所有飞线已清除");
  }

  /**
   * 获取所有飞线信息
   */
  getFlightLines() {
    return this.flightLines.map((flight) => ({
      id: flight.id,
      startCoords: flight.startCoords,
      endCoords: flight.endCoords,
      options: flight.options,
    }));
  }

  /**
   * 更新所有点的位置
   */
  updateAllPoints() {
    // 更新shader系统的点
    this.pointsData.forEach((pointData) => {
      if (pointData.originalCoords) {
        const { latitude, longitude } = pointData.originalCoords;
        const newPosition = this.spherePoints.latLonToVector3(latitude, longitude);
        pointData.position.copy(newPosition);
      }
    });

    // 更新shader几何体
    if (this.pointsData.length > 0) {
      this.updatePointsGeometry();
    }

    console.log("所有shader点位置已更新");
  }

  /**
   * 销毁所有飞线资源
   */
  destroy() {
    this.clearAllFlightLines();

    // 停止动画循环
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }

    // 清理GUI
    if (this.gui) {
      this.gui.destroy();
      this.gui = null;
    }

    // 清理shader系统
    if (this.pointsSystem) {
      this.spherePoints.pointsGroup.remove(this.pointsSystem);
      this.pointsSystem = null;
    }

    if (this.pointsGeometry) {
      this.pointsGeometry.dispose();
      this.pointsGeometry = null;
    }

    if (this.pointsMaterial) {
      this.pointsMaterial.dispose();
      this.pointsMaterial = null;
    }

    // 清理点数据
    this.pointsData = [];

    this.spherePoints = null;
  }

  /**
   * 曲线粒子动画（头大尾小效果）
   * @param {object} particleData - 粒子数据对象
   * @param {Float32Array} pathPositions - 路径位置数组
   * @param {number} duration - 动画持续时间
   * @param {number} delay - 延迟时间
   */
  animateCurveParticle(particleData, pathPositions, duration, delay) {
    const startTime = Date.now() + delay;
    const pointCount = pathPositions.length / 3;

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.max(0, Math.min(1, elapsed / duration));

      if (progress >= 1) {
        // 动画结束，重新开始
        setTimeout(() => {
          this.animateCurveParticle(particleData, pathPositions, duration, 0);
        }, 1000);
        return;
      }

      // 更新粒子的动画进度
      particleData.progress = progress;

      // 计算当前位置索引
      const positionIndex = Math.floor(progress * (pointCount - 1)) * 3;

      // 确保索引有效
      const safeIndex = Math.max(0, Math.min(positionIndex, pathPositions.length - 3));

      // 更新粒子位置
      particleData.position.x = pathPositions[safeIndex];
      particleData.position.y = pathPositions[safeIndex + 1];
      particleData.position.z = pathPositions[safeIndex + 2];

      // 更新几何体
      this.updatePointsGeometry();

      requestAnimationFrame(animate);
    };

    // 延迟启动动画
    if (delay > 0) {
      setTimeout(animate, delay);
    } else {
      animate();
    }
  }

  /**
   * 更新曲线粒子数量
   * @param {number} newCount - 新的粒子数量
   */
  updateCurveParticleCount(newCount) {
    this.shaderParams.curveParticleCount = newCount;

    // 重新创建所有飞线的粒子系统
    this.flightLines.forEach((flight) => {
      if (flight.particles && flight.particles.length > 0) {
        // 移除旧的粒子
        flight.particles.forEach((particle) => {
          const index = this.pointsData.findIndex((p) => p.id === particle.id);
          if (index > -1) {
            this.pointsData.splice(index, 1);
          }
        });

        // 重新创建粒子
        flight.particles = this.createFlightParticlesWithShader(flight.line, flight.options.finalAnimationOptions);
      }
    });

    // 更新几何体
    this.updatePointsGeometry();

    console.log(`曲线粒子数量已更新为: ${newCount}`);
  }

  /**
   * 淡入显示PlaneMarker标识
   * @param {THREE.Mesh} marker - PlaneMarker对象
   * @param {number} duration - 淡入持续时间（毫秒）
   */
  fadeInMarker(marker, duration = 800) {
    if (!marker || !marker.userData.isGlowRingMarker) {
      console.warn("fadeInMarker: 无效的PlaneMarker对象");
      return;
    }

    // 停止任何正在进行的淡入/淡出动画
    if (marker.userData.fadeAnimationId) {
      cancelAnimationFrame(marker.userData.fadeAnimationId);
    }

    // 设置初始状态
    marker.visible = true;
    const targetOpacity = marker.userData.glowConfig.opacity || 0.8;

    // 设置初始透明度为0
    if (marker.material.uniforms.uOpacity) {
      marker.material.uniforms.uOpacity.value = 0.0;
    }

    const startTime = performance.now();

    const animate = (currentTime) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 使用easeOutCubic缓动函数
      const easedProgress = 1 - Math.pow(1 - progress, 3);
      const currentOpacity = easedProgress * targetOpacity;

      // 更新透明度
      if (marker.material.uniforms.uOpacity) {
        marker.material.uniforms.uOpacity.value = currentOpacity;
      }

      if (progress >= 1) {
        // 动画完成
        marker.userData.fadeAnimationId = null;
        console.log(`PlaneMarker淡入动画完成: ${marker.name}`);
        return;
      }

      marker.userData.fadeAnimationId = requestAnimationFrame(animate);
    };

    console.log(`开始PlaneMarker淡入动画: ${marker.name}, 持续时间: ${duration}ms`);
    marker.userData.fadeAnimationId = requestAnimationFrame(animate);
  }

  /**
   * 淡出隐藏PlaneMarker标识
   * @param {THREE.Mesh} marker - PlaneMarker对象
   * @param {number} duration - 淡出持续时间（毫秒）
   */
  fadeOutMarker(marker, duration = 600) {
    if (!marker || !marker.userData.isGlowRingMarker) {
      console.warn("fadeOutMarker: 无效的PlaneMarker对象");
      return;
    }

    // 停止任何正在进行的淡入/淡出动画
    if (marker.userData.fadeAnimationId) {
      cancelAnimationFrame(marker.userData.fadeAnimationId);
    }

    // 获取当前透明度
    const startOpacity = marker.material.uniforms.uOpacity ? marker.material.uniforms.uOpacity.value : 0.8;
    const startTime = performance.now();

    const animate = (currentTime) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 使用easeInCubic缓动函数
      const easedProgress = Math.pow(progress, 3);
      const currentOpacity = startOpacity * (1 - easedProgress);

      // 更新透明度
      if (marker.material.uniforms.uOpacity) {
        marker.material.uniforms.uOpacity.value = currentOpacity;
      }

      if (progress >= 1) {
        // 动画完成，隐藏标识
        marker.visible = false;
        marker.userData.fadeAnimationId = null;
        console.log(`PlaneMarker淡出动画完成: ${marker.name}`);
        return;
      }

      marker.userData.fadeAnimationId = requestAnimationFrame(animate);
    };

    console.log(`开始PlaneMarker淡出动画: ${marker.name}, 持续时间: ${duration}ms`);
    marker.userData.fadeAnimationId = requestAnimationFrame(animate);
  }

  /**
   * 设置飞线悬停状态
   * @param {Object} flightLineData - 飞线数据
   * @param {boolean} isHovered - 是否悬停
   */
  setFlightLineHoverState(flightLineData, isHovered) {
    if (!flightLineData) {
      console.warn("setFlightLineHoverState: flightLineData 为空");
      return;
    }

    try {
      const hoverColor = 0xffff00; // 黄色
      const originalColor = flightLineData.options?.finalLineOptions?.color || 0x00ffff;

      if (isHovered) {
        // 设置悬停样式
        this.setFlightLineColor(flightLineData, hoverColor);
        this.setFlightLineOpacity(flightLineData, 1.0);
        flightLineData.isHovered = true;
        console.log("飞线悬停状态:", flightLineData.id);
      } else {
        // 恢复原始样式（如果不是点击状态）
        if (!flightLineData.isClicked) {
          this.setFlightLineColor(flightLineData, originalColor);
          this.setFlightLineOpacity(flightLineData, 0.8);
        }
        flightLineData.isHovered = false;
      }
    } catch (error) {
      console.error("设置飞线悬停状态失败:", error, flightLineData);
    }
  }

  /**
   * 设置飞线点击状态
   * @param {Object} flightLineData - 飞线数据
   * @param {boolean} isClicked - 是否被点击
   */
  setFlightLineClickedState(flightLineData, isClicked) {
    if (!flightLineData) {
      console.warn("setFlightLineClickedState: flightLineData 为空");
      return;
    }

    try {
      const clickedColor = 0xff0000; // 红色
      const originalColor = flightLineData.options?.finalLineOptions?.color || 0x00ffff;

      if (isClicked) {
        // 重置其他飞线的点击状态
        this.resetAllClickedStates();

        // 设置点击样式
        this.setFlightLineColor(flightLineData, clickedColor);
        this.setFlightLineOpacity(flightLineData, 1.0);
        flightLineData.isClicked = true;
        console.log("飞线点击状态:", flightLineData.id);
      } else {
        // 恢复原始样式
        this.setFlightLineColor(flightLineData, originalColor);
        this.setFlightLineOpacity(flightLineData, 0.8);
        flightLineData.isClicked = false;
      }
    } catch (error) {
      console.error("设置飞线点击状态失败:", error, flightLineData);
    }
  }

  /**
   * 设置飞线颜色
   * @param {Object} flightLineData - 飞线数据
   * @param {number} color - 颜色值
   */
  setFlightLineColor(flightLineData, color) {
    try {
      const colorObj = new THREE.Color(color);

      // 设置线条颜色 (ShaderMaterial)
      if (flightLineData.line && flightLineData.line.material) {
        if (flightLineData.line.material.uniforms && flightLineData.line.material.uniforms.color) {
          flightLineData.line.material.uniforms.color.value.copy(colorObj);
          flightLineData.line.material.needsUpdate = true;
        }
        // 备用：普通材质
        else if (flightLineData.line.material.color) {
          flightLineData.line.material.color.copy(colorObj);
        }
      }

      // 设置粒子颜色 (ShaderMaterial with startColor/endColor)
      if (flightLineData.particles && Array.isArray(flightLineData.particles)) {
        flightLineData.particles.forEach((particle) => {
          if (particle && particle.material && particle.material.uniforms) {
            // 粒子使用 startColor 和 endColor
            if (particle.material.uniforms.startColor) {
              particle.material.uniforms.startColor.value.copy(colorObj);
            }
            if (particle.material.uniforms.endColor) {
              particle.material.uniforms.endColor.value.copy(colorObj);
            }
            // 备用：单一颜色
            if (particle.material.uniforms.color) {
              particle.material.uniforms.color.value.copy(colorObj);
            }
            particle.material.needsUpdate = true;
          }
        });
      }

      // 设置起点和终点标识颜色
      if (flightLineData.startPoint && flightLineData.startPoint.material) {
        if (flightLineData.startPoint.material.color) {
          flightLineData.startPoint.material.color.copy(colorObj);
        }
      }
      if (flightLineData.endPoint && flightLineData.endPoint.material) {
        if (flightLineData.endPoint.material.color) {
          flightLineData.endPoint.material.color.copy(colorObj);
        }
      }

      console.log(`飞线颜色已设置为: #${color.toString(16).padStart(6, "0")}`);
    } catch (error) {
      console.warn("设置飞线颜色失败:", error, flightLineData);
    }
  }

  /**
   * 设置飞线透明度
   * @param {Object} flightLineData - 飞线数据
   * @param {number} opacity - 透明度值
   */
  setFlightLineOpacity(flightLineData, opacity) {
    try {
      // 设置线条透明度 (ShaderMaterial)
      if (flightLineData.line && flightLineData.line.material) {
        if (flightLineData.line.material.uniforms && flightLineData.line.material.uniforms.opacity) {
          flightLineData.line.material.uniforms.opacity.value = opacity;
          flightLineData.line.material.needsUpdate = true;
        }
        // 备用：普通材质
        else if (flightLineData.line.material.opacity !== undefined) {
          flightLineData.line.material.opacity = opacity;
          flightLineData.line.material.transparent = true;
        }
      }

      // 设置粒子透明度 (ShaderMaterial)
      if (flightLineData.particles && Array.isArray(flightLineData.particles)) {
        flightLineData.particles.forEach((particle) => {
          if (particle && particle.material) {
            // 粒子材质通常没有单独的opacity uniform，透明度通过颜色的alpha通道控制
            // 但我们可以尝试设置材质的整体透明度
            if (particle.material.opacity !== undefined) {
              particle.material.opacity = opacity;
              particle.material.transparent = true;
              particle.material.needsUpdate = true;
            }
          }
        });
      }

      // 设置起点和终点标识透明度
      if (flightLineData.startPoint && flightLineData.startPoint.material) {
        flightLineData.startPoint.material.opacity = opacity;
        flightLineData.startPoint.material.transparent = true;
      }
      if (flightLineData.endPoint && flightLineData.endPoint.material) {
        flightLineData.endPoint.material.opacity = opacity;
        flightLineData.endPoint.material.transparent = true;
      }

      console.log(`飞线透明度已设置为: ${opacity}`);
    } catch (error) {
      console.warn("设置飞线透明度失败:", error, flightLineData);
    }
  }

  /**
   * 重置所有飞线的点击状态
   */
  resetAllClickedStates() {
    try {
      this.flightLines.forEach((flightLineData) => {
        if (flightLineData && flightLineData.isClicked) {
          this.setFlightLineClickedState(flightLineData, false);
        }
      });
    } catch (error) {
      console.error("重置所有点击状态失败:", error);
    }
  }

  /**
   * 重置所有飞线状态
   */
  resetAllFlightLineStates() {
    try {
      this.flightLines.forEach((flightLineData) => {
        if (flightLineData) {
          flightLineData.isHovered = false;
          flightLineData.isClicked = false;

          const originalColor = flightLineData.options?.finalLineOptions?.color || 0x00ffff;
          this.setFlightLineColor(flightLineData, originalColor);
          this.setFlightLineOpacity(flightLineData, 0.8);
        }
      });
    } catch (error) {
      console.error("重置所有飞线状态失败:", error);
    }
  }
}

/**
 * 标签大小配置使用示例：
 *
 * // 创建带大标签的飞线
 * flightLine.createFlightLineWithShader(39.9042, 116.4074, 35.6762, 139.6503, {
 *   labelOptions: {
 *     text: "攻击路径",
 *     fontSize: 48,        // 更大的字体
 *     scale: 2.0,          // 更大的整体缩放
 *     color: "#ffff00",    // 黄色文字
 *     borderWidth: 3,      // 更粗的边框
 *     padding: 16,         // 更大的内边距
 *   }
 * });
 *
 * // 创建小标签的飞线
 * flightLine.createFlightLineWithShader(39.9042, 116.4074, 35.6762, 139.6503, {
 *   labelOptions: {
 *     fontSize: 24,        // 较小的字体
 *     scale: 1.0,          // 正常缩放
 *     padding: 8,          // 较小的内边距
 *   }
 * });
 *
 * // 禁用标签的飞线
 * flightLine.createFlightLineWithShader(39.9042, 116.4074, 35.6762, 139.6503, {
 *   labelOptions: {
 *     enabled: false       // 不显示标签
 *   }
 * });
 */

export { FlightLine };
export default FlightLine;
