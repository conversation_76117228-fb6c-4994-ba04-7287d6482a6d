import * as THREE from "three";

/**
 * 3D文字标签工具类
 * 
 * 用于在3D场景中创建文字标签，支持多种样式和动画效果。
 * 使用Canvas纹理技术在地球表面显示文字。
 * 
 * 功能特性：
 * - 支持中文和英文文字渲染
 * - 可自定义字体、大小、颜色
 * - 支持背景色和边框
 * - 自动朝向相机
 * - 支持淡入淡出动画
 * - 自动资源管理
 * 
 * 使用示例：
 * ```javascript
 * import TextLabel from './TextLabel.js';
 * 
 * // 创建简单文字标签
 * const label = TextLabel.createTextLabel(position, "标签", {
 *   fontSize: 32,
 *   color: "#ffffff",
 *   backgroundColor: "rgba(0,0,0,0.7)"
 * });
 * scene.add(label);
 * 
 * // 创建带动画的标签
 * TextLabel.fadeInLabel(label, 1000);
 * ```
 */
class TextLabel {
  /**
   * 创建文字标签
   * @param {THREE.Vector3} position - 标签位置
   * @param {string} text - 显示文字
   * @param {Object} options - 配置选项
   * @returns {THREE.Mesh} 文字标签网格对象
   */
  static createTextLabel(position, text = "标签", options = {}) {
    const defaultOptions = {
      fontSize: 32,
      fontFamily: "Arial, sans-serif",
      color: "#ffffff",
      backgroundColor: "rgba(0, 0, 0, 0.7)",
      borderColor: "#ffffff",
      borderWidth: 2,
      padding: 10,
      borderRadius: 8,
      maxWidth: 200,
      textAlign: "center",
      name: `TextLabel_${Date.now()}`,
      scale: 1.0,
      opacity: 1.0,
      autoFaceCamera: true,
    };

    const config = { ...defaultOptions, ...options };

    // 创建Canvas纹理
    const canvas = this._createTextCanvas(text, config);
    const texture = new THREE.CanvasTexture(canvas);
    texture.needsUpdate = true;

    // 计算标签尺寸（基于Canvas尺寸）
    const aspect = canvas.width / canvas.height;
    const labelWidth = config.scale;
    const labelHeight = labelWidth / aspect;

    // 创建平面几何体
    const geometry = new THREE.PlaneGeometry(labelWidth, labelHeight);

    // 创建材质
    const material = new THREE.MeshBasicMaterial({
      map: texture,
      transparent: true,
      opacity: config.opacity,
      side: THREE.DoubleSide,
      alphaTest: 0.1, // 避免透明部分的渲染问题
    });

    // 创建网格
    const mesh = new THREE.Mesh(geometry, material);

    // 设置位置
    mesh.position.copy(position);

    // 设置朝向（朝向相机）
    if (config.autoFaceCamera) {
      this._setLabelOrientation(mesh, position);
    }

    // 设置名称
    mesh.name = config.name;

    // 存储配置和资源引用
    mesh.userData = {
      isTextLabel: true,
      text: text,
      config: config,
      canvas: canvas,
      texture: texture,
      originalPosition: position.clone(),
    };

    return mesh;
  }

  /**
   * 创建Canvas文字纹理
   * @private
   * @param {string} text - 文字内容
   * @param {Object} config - 配置选项
   * @returns {HTMLCanvasElement} Canvas元素
   */
  static _createTextCanvas(text, config) {
    const canvas = document.createElement("canvas");
    const context = canvas.getContext("2d");

    // 设置字体以测量文字尺寸
    context.font = `${config.fontSize}px ${config.fontFamily}`;
    
    // 测量文字尺寸
    const metrics = context.measureText(text);
    const textWidth = metrics.width;
    const textHeight = config.fontSize;

    // 计算Canvas尺寸（包含padding）
    const canvasWidth = Math.min(textWidth + config.padding * 2, config.maxWidth + config.padding * 2);
    const canvasHeight = textHeight + config.padding * 2;

    // 设置Canvas尺寸（使用2的幂次方以获得更好的性能）
    canvas.width = this._nextPowerOfTwo(canvasWidth);
    canvas.height = this._nextPowerOfTwo(canvasHeight);

    // 重新设置字体（Canvas尺寸改变后需要重新设置）
    context.font = `${config.fontSize}px ${config.fontFamily}`;
    context.textAlign = config.textAlign;
    context.textBaseline = "middle";

    // 绘制背景
    if (config.backgroundColor && config.backgroundColor !== "transparent") {
      context.fillStyle = config.backgroundColor;
      
      if (config.borderRadius > 0) {
        // 绘制圆角矩形背景
        this._drawRoundedRect(
          context,
          config.padding / 2,
          config.padding / 2,
          canvasWidth - config.padding,
          canvasHeight - config.padding,
          config.borderRadius
        );
      } else {
        // 绘制普通矩形背景
        context.fillRect(
          config.padding / 2,
          config.padding / 2,
          canvasWidth - config.padding,
          canvasHeight - config.padding
        );
      }
    }

    // 绘制边框
    if (config.borderWidth > 0 && config.borderColor) {
      context.strokeStyle = config.borderColor;
      context.lineWidth = config.borderWidth;
      
      if (config.borderRadius > 0) {
        // 绘制圆角矩形边框
        this._drawRoundedRect(
          context,
          config.padding / 2,
          config.padding / 2,
          canvasWidth - config.padding,
          canvasHeight - config.padding,
          config.borderRadius
        );
        context.stroke();
      } else {
        // 绘制普通矩形边框
        context.strokeRect(
          config.padding / 2,
          config.padding / 2,
          canvasWidth - config.padding,
          canvasHeight - config.padding
        );
      }
    }

    // 绘制文字
    context.fillStyle = config.color;
    
    // 计算文字位置
    let textX;
    if (config.textAlign === "center") {
      textX = canvas.width / 2;
    } else if (config.textAlign === "right") {
      textX = canvas.width - config.padding;
    } else {
      textX = config.padding;
    }
    
    const textY = canvas.height / 2;

    // 处理文字换行（如果文字太长）
    if (textWidth > config.maxWidth) {
      this._drawWrappedText(context, text, textX, textY, config.maxWidth, config.fontSize);
    } else {
      context.fillText(text, textX, textY);
    }

    return canvas;
  }

  /**
   * 绘制圆角矩形
   * @private
   */
  static _drawRoundedRect(context, x, y, width, height, radius) {
    context.beginPath();
    context.moveTo(x + radius, y);
    context.lineTo(x + width - radius, y);
    context.quadraticCurveTo(x + width, y, x + width, y + radius);
    context.lineTo(x + width, y + height - radius);
    context.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    context.lineTo(x + radius, y + height);
    context.quadraticCurveTo(x, y + height, x, y + height - radius);
    context.lineTo(x, y + radius);
    context.quadraticCurveTo(x, y, x + radius, y);
    context.closePath();
    context.fill();
  }

  /**
   * 绘制换行文字
   * @private
   */
  static _drawWrappedText(context, text, x, y, maxWidth, lineHeight) {
    const words = text.split("");
    let line = "";
    let testLine = "";
    let metrics = null;
    let testWidth = 0;
    
    for (let n = 0; n < words.length; n++) {
      testLine = line + words[n];
      metrics = context.measureText(testLine);
      testWidth = metrics.width;
      
      if (testWidth > maxWidth && n > 0) {
        context.fillText(line, x, y);
        line = words[n];
        y += lineHeight;
      } else {
        line = testLine;
      }
    }
    context.fillText(line, x, y);
  }

  /**
   * 获取下一个2的幂次方数
   * @private
   */
  static _nextPowerOfTwo(value) {
    return Math.pow(2, Math.ceil(Math.log2(value)));
  }

  /**
   * 设置标签朝向
   * @private
   */
  static _setLabelOrientation(mesh, position) {
    // 计算从地球中心指向位置的方向向量
    const direction = position.clone().normalize();
    
    // 让标签法向量指向地球中心外侧（朝向观察者）
    mesh.lookAt(position.x + direction.x, position.y + direction.y, position.z + direction.z);
  }

  /**
   * 更新标签文字
   * @param {THREE.Mesh} label - 标签对象
   * @param {string} newText - 新文字内容
   */
  static updateLabelText(label, newText) {
    if (!label || !label.userData.isTextLabel) {
      console.warn("updateLabelText: 无效的文字标签对象");
      return;
    }

    // 更新文字内容
    label.userData.text = newText;

    // 重新创建Canvas纹理
    const newCanvas = this._createTextCanvas(newText, label.userData.config);
    label.userData.canvas = newCanvas;

    // 更新纹理
    label.userData.texture.image = newCanvas;
    label.userData.texture.needsUpdate = true;

    console.log(`文字标签已更新: ${label.name} -> "${newText}"`);
  }

  /**
   * 淡入显示标签
   * @param {THREE.Mesh} label - 标签对象
   * @param {number} duration - 淡入持续时间（毫秒）
   */
  static fadeInLabel(label, duration = 800) {
    if (!label || !label.userData.isTextLabel) {
      console.warn("fadeInLabel: 无效的文字标签对象");
      return;
    }

    // 停止任何正在进行的动画
    if (label.userData.fadeAnimationId) {
      cancelAnimationFrame(label.userData.fadeAnimationId);
    }

    // 设置初始状态
    label.visible = true;
    const targetOpacity = label.userData.config.opacity || 1.0;
    label.material.opacity = 0.0;

    const startTime = performance.now();

    const animate = (currentTime) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 使用easeOutCubic缓动函数
      const easedProgress = 1 - Math.pow(1 - progress, 3);
      label.material.opacity = easedProgress * targetOpacity;

      if (progress >= 1) {
        // 动画完成
        label.userData.fadeAnimationId = null;
        console.log(`文字标签淡入动画完成: ${label.name}`);
        return;
      }

      label.userData.fadeAnimationId = requestAnimationFrame(animate);
    };

    console.log(`开始文字标签淡入动画: ${label.name}, 持续时间: ${duration}ms`);
    label.userData.fadeAnimationId = requestAnimationFrame(animate);
  }

  /**
   * 淡出隐藏标签
   * @param {THREE.Mesh} label - 标签对象
   * @param {number} duration - 淡出持续时间（毫秒）
   */
  static fadeOutLabel(label, duration = 600) {
    if (!label || !label.userData.isTextLabel) {
      console.warn("fadeOutLabel: 无效的文字标签对象");
      return;
    }

    // 停止任何正在进行的动画
    if (label.userData.fadeAnimationId) {
      cancelAnimationFrame(label.userData.fadeAnimationId);
    }

    // 获取当前透明度
    const startOpacity = label.material.opacity;
    const startTime = performance.now();

    const animate = (currentTime) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 使用easeInCubic缓动函数
      const easedProgress = Math.pow(progress, 3);
      label.material.opacity = startOpacity * (1 - easedProgress);

      if (progress >= 1) {
        // 动画完成，隐藏标签
        label.visible = false;
        label.userData.fadeAnimationId = null;
        console.log(`文字标签淡出动画完成: ${label.name}`);
        return;
      }

      label.userData.fadeAnimationId = requestAnimationFrame(animate);
    };

    console.log(`开始文字标签淡出动画: ${label.name}, 持续时间: ${duration}ms`);
    label.userData.fadeAnimationId = requestAnimationFrame(animate);
  }

  /**
   * 销毁文字标签
   * @param {THREE.Mesh} label - 标签对象
   */
  static destroyLabel(label) {
    if (!label || !label.userData.isTextLabel) {
      console.warn("destroyLabel: 无效的文字标签对象");
      return;
    }

    // 停止动画
    if (label.userData.fadeAnimationId) {
      cancelAnimationFrame(label.userData.fadeAnimationId);
    }

    // 清理资源
    if (label.geometry) label.geometry.dispose();
    if (label.material) {
      if (label.material.map) label.material.map.dispose();
      label.material.dispose();
    }

    console.log(`文字标签已销毁: ${label.name}`);
  }

  /**
   * 批量销毁标签
   * @param {Array} labels - 标签对象数组
   */
  static destroyMultipleLabels(labels) {
    if (Array.isArray(labels)) {
      labels.forEach((label) => this.destroyLabel(label));
      console.log(`已销毁 ${labels.length} 个文字标签`);
    }
  }
}

export default TextLabel;
